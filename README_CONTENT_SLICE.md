# Content Slice API

## Tổng quan

API Content Slice cho phép cắt nội dung bài học theo vị trí ký tự cụ thể. API này đã được tích hợp vào hệ thống PlanBook AI và sẵn sàng sử dụng.

## Endpoint

```
POST /api/slides/content-slice
```

## Cách sử dụng nhanh

### 1. Request cơ bản

```json
{
  "lesson_id": "your_lesson_id",
  "start_char": 0,
  "end_char": 100
}
```

### 2. Request với book_id

```json
{
  "book_id": "your_book_id",
  "lesson_id": "your_lesson_id", 
  "start_char": 100,
  "end_char": 500
}
```

## Files được tạo

1. **`app/models/slide_generation_models.py`** - Đã thêm models:
   - `ContentSliceRequest`
   - `ContentSliceResponse`

2. **`app/api/endpoints/slide_generation.py`** - <PERSON><PERSON> thêm endpoint:
   - `POST /content-slice`

3. **`test_content_slice_api.py`** - Script test API

4. **`example_usage.py`** - Ví dụ sử dụng với Python client

5. **`CONTENT_SLICE_API_DOCS.md`** - Documentation chi tiết

## Chạy test

```bash
# Test cơ bản
python test_content_slice_api.py

# Demo sử dụng
python example_usage.py
```

## Lưu ý

- Cần cập nhật `lesson_id` thực tế trong các file test
- API tự động tìm lesson trong Qdrant collections
- Hỗ trợ cả tìm kiếm theo `book_id` cụ thể hoặc tìm trong tất cả collections

## Tính năng chính

✅ Cắt nội dung theo vị trí ký tự chính xác  
✅ Validation đầu vào  
✅ Xử lý lỗi chi tiết  
✅ Tự động điều chỉnh nếu vượt quá độ dài nội dung  
✅ Hỗ trợ tìm kiếm theo book_id  
✅ Trả về metadata đầy đủ  

## API Response

```json
{
  "success": true,
  "book_id": "book_123",
  "lesson_id": "lesson_456",
  "start_char": 0,
  "end_char": 100,
  "sliced_content": "Nội dung đã cắt...",
  "original_content_length": 2500,
  "sliced_content_length": 100,
  "collection_name": "textbook_book_123"
}
```

API đã sẵn sàng sử dụng! 🚀
