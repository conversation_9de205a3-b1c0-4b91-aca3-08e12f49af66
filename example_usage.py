"""
Ví dụ sử dụng Content Slice API
"""

import requests
import json
from typing import Optional

class ContentSliceClient:
    """Client để gọi Content Slice API"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_endpoint = f"{base_url}/api/slides/content-slice"
    
    def get_content_slice(
        self, 
        lesson_id: str, 
        start_char: int, 
        end_char: int,
        book_id: Optional[str] = None
    ) -> dict:
        """
        Lấy nội dung đã cắt từ bài học
        
        Args:
            lesson_id: ID của bài học
            start_char: <PERSON><PERSON> trí ký tự bắt đầu (0-based)
            end_char: <PERSON>ị trí ký tự kết thúc (0-based, exclusive)
            book_id: ID của sách (optional)
            
        Returns:
            dict: Response từ API
        """
        request_data = {
            "lesson_id": lesson_id,
            "start_char": start_char,
            "end_char": end_char
        }
        
        if book_id:
            request_data["book_id"] = book_id
        
        try:
            response = requests.post(
                self.api_endpoint,
                json=request_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}",
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Request failed: {str(e)}"
            }
    
    def get_content_preview(self, lesson_id: str, preview_length: int = 200, book_id: Optional[str] = None) -> dict:
        """Lấy preview đầu của nội dung bài học"""
        return self.get_content_slice(lesson_id, 0, preview_length, book_id)
    
    def get_content_chunks(self, lesson_id: str, chunk_size: int = 500, book_id: Optional[str] = None) -> list:
        """
        Chia nội dung thành các chunks nhỏ
        
        Returns:
            list: Danh sách các chunks nội dung
        """
        chunks = []
        start_pos = 0
        
        # Lấy độ dài nội dung trước
        first_chunk = self.get_content_slice(lesson_id, 0, chunk_size, book_id)
        if not first_chunk.get("success"):
            return [first_chunk]  # Return error
        
        total_length = first_chunk.get("original_content_length", 0)
        chunks.append(first_chunk)
        
        # Lấy các chunks tiếp theo
        start_pos = chunk_size
        while start_pos < total_length:
            end_pos = min(start_pos + chunk_size, total_length)
            chunk = self.get_content_slice(lesson_id, start_pos, end_pos, book_id)
            
            if chunk.get("success"):
                chunks.append(chunk)
            else:
                break  # Stop on error
                
            start_pos = end_pos
        
        return chunks

def demo_basic_usage():
    """Demo sử dụng cơ bản"""
    print("=== Demo Content Slice API ===\n")
    
    client = ContentSliceClient()
    
    # Thay đổi lesson_id này thành lesson_id thực tế trong database của bạn
    lesson_id = "lesson_456"  # TODO: Thay bằng lesson_id thực tế
    book_id = None  # Hoặc "book_123" nếu muốn tìm trong sách cụ thể
    
    print("1. Lấy preview nội dung (200 ký tự đầu):")
    preview = client.get_content_preview(lesson_id, 200, book_id)
    
    if preview.get("success"):
        print(f"✅ Preview thành công!")
        print(f"📊 Độ dài gốc: {preview.get('original_content_length')} ký tự")
        print(f"📝 Preview: {preview.get('sliced_content')[:100]}...")
    else:
        print(f"❌ Lỗi: {preview.get('error')}")
        return
    
    print("\n" + "="*50 + "\n")
    
    print("2. Lấy nội dung từ ký tự 100-300:")
    middle_content = client.get_content_slice(lesson_id, 100, 300, book_id)
    
    if middle_content.get("success"):
        print(f"✅ Lấy nội dung thành công!")
        print(f"📝 Nội dung: {middle_content.get('sliced_content')}")
    else:
        print(f"❌ Lỗi: {middle_content.get('error')}")
    
    print("\n" + "="*50 + "\n")
    
    print("3. Chia nội dung thành chunks 500 ký tự:")
    chunks = client.get_content_chunks(lesson_id, 500, book_id)
    
    if chunks and chunks[0].get("success"):
        print(f"✅ Chia thành {len(chunks)} chunks!")
        for i, chunk in enumerate(chunks):
            if chunk.get("success"):
                content_preview = chunk.get('sliced_content', '')[:50] + "..."
                print(f"  Chunk {i+1}: {chunk.get('sliced_content_length')} ký tự - {content_preview}")
    else:
        print(f"❌ Lỗi khi chia chunks: {chunks[0].get('error') if chunks else 'No chunks returned'}")

def demo_error_handling():
    """Demo xử lý lỗi"""
    print("\n=== Demo Error Handling ===\n")
    
    client = ContentSliceClient()
    
    print("1. Test với lesson_id không tồn tại:")
    result = client.get_content_slice("invalid_lesson_id", 0, 100)
    print(f"Result: {result.get('error', 'No error')}")
    
    print("\n2. Test với range không hợp lệ (end < start):")
    result = client.get_content_slice("lesson_456", 100, 50)  # Invalid range
    print(f"Result: {result.get('error', 'No error')}")

if __name__ == "__main__":
    print("🚀 Starting Content Slice API Demo")
    print("📝 Make sure to update lesson_id with a real lesson ID from your database\n")
    
    demo_basic_usage()
    demo_error_handling()
    
    print("\n✅ Demo completed!")
    print("💡 Tip: Check CONTENT_SLICE_API_DOCS.md for detailed documentation")
