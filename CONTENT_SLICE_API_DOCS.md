# Content Slice API Documentation

## Tổng quan

API Content Slice cho phép cắt nội dung bài học theo vị trí ký tự cụ thể. API này hữu ích khi bạn cần lấy một phần nội dung cụ thể từ bài học để hiển thị hoặc xử lý.

## Endpoint

```
POST /api/slides/content-slice
```

## Request Model

### ContentSliceRequest

```json
{
  "book_id": "string (optional)",
  "lesson_id": "string (required)",
  "start_char": "integer (required, >= 0)",
  "end_char": "integer (required, > start_char)"
}
```

### Tham số

- **book_id** (optional): ID của sách giáo khoa. Nếu có thì chỉ tìm lesson trong collection `textbook_{book_id}`
- **lesson_id** (required): ID của bài học cần lấy nội dung
- **start_char** (required): <PERSON><PERSON> trí ký tự bắt đầu (0-based index)
- **end_char** (required): Vị trí ký tự kết thúc (0-based index, exclusive)

### Validation Rules

- `start_char` phải >= 0
- `end_char` phải > `start_char`
- `lesson_id` không được rỗng

## Response Model

### ContentSliceResponse

```json
{
  "success": "boolean",
  "book_id": "string",
  "lesson_id": "string", 
  "start_char": "integer",
  "end_char": "integer",
  "sliced_content": "string",
  "original_content_length": "integer",
  "sliced_content_length": "integer",
  "collection_name": "string",
  "error": "string"
}
```

### Trường dữ liệu

- **success**: Trạng thái thành công của request
- **book_id**: ID của sách giáo khoa (từ database)
- **lesson_id**: ID của bài học
- **start_char**: Vị trí ký tự bắt đầu đã sử dụng
- **end_char**: Vị trí ký tự kết thúc đã sử dụng (có thể được điều chỉnh nếu vượt quá độ dài nội dung)
- **sliced_content**: Nội dung đã được cắt
- **original_content_length**: Độ dài nội dung gốc
- **sliced_content_length**: Độ dài nội dung đã cắt
- **collection_name**: Tên collection Qdrant đã tìm thấy lesson
- **error**: Thông báo lỗi (nếu có)

## Ví dụ sử dụng

### 1. Request cơ bản

```bash
curl -X POST "http://localhost:8000/api/slides/content-slice" \
  -H "Content-Type: application/json" \
  -d '{
    "lesson_id": "lesson_456",
    "start_char": 0,
    "end_char": 100
  }'
```

### 2. Request với book_id cụ thể

```bash
curl -X POST "http://localhost:8000/api/slides/content-slice" \
  -H "Content-Type: application/json" \
  -d '{
    "book_id": "book_123",
    "lesson_id": "lesson_456", 
    "start_char": 100,
    "end_char": 500
  }'
```

### 3. Response thành công

```json
{
  "success": true,
  "book_id": "book_123",
  "lesson_id": "lesson_456",
  "start_char": 100,
  "end_char": 500,
  "sliced_content": "Đây là nội dung đã được cắt từ vị trí ký tự 100 đến 500...",
  "original_content_length": 2500,
  "sliced_content_length": 400,
  "collection_name": "textbook_book_123"
}
```

## Xử lý lỗi

### 1. Lesson không tồn tại

```json
{
  "detail": "Lesson lesson_456 not found in any textbook collection"
}
```

### 2. Vị trí ký tự không hợp lệ

```json
{
  "detail": {
    "error_code": "INVALID_START_POSITION",
    "error_message": "start_char (1000) vượt quá độ dài nội dung (500)",
    "success": false
  }
}
```

### 3. Validation error

```json
{
  "detail": [
    {
      "loc": ["body", "end_char"],
      "msg": "end_char phải lớn hơn start_char",
      "type": "value_error"
    }
  ]
}
```

## Lưu ý quan trọng

1. **Character Indexing**: API sử dụng 0-based indexing (ký tự đầu tiên có index 0)

2. **End Character**: `end_char` là exclusive, nghĩa là ký tự tại vị trí `end_char` sẽ không được bao gồm

3. **Auto Adjustment**: Nếu `end_char` vượt quá độ dài nội dung, API sẽ tự động điều chỉnh về độ dài tối đa

4. **Performance**: API lấy toàn bộ nội dung lesson từ Qdrant trước khi cắt, phù hợp cho các lesson có kích thước vừa phải

5. **Collection Search**: Nếu không có `book_id`, API sẽ tìm trong tất cả collections có pattern `textbook_*`

## Use Cases

- **Preview Content**: Hiển thị preview của nội dung bài học
- **Pagination**: Chia nội dung thành các trang nhỏ
- **Excerpt Generation**: Tạo đoạn trích dẫn từ bài học
- **Content Analysis**: Phân tích một phần cụ thể của nội dung
- **Slide Generation**: Lấy nội dung cho từng slide cụ thể

## Testing

Sử dụng file `test_content_slice_api.py` để test API:

```bash
python test_content_slice_api.py
```

File test bao gồm:
- Test case cơ bản
- Test các trường hợp biên (edge cases)
- Test validation errors
