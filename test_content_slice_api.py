"""
Test script cho Content Slice API
"""

import requests
import json

# <PERSON><PERSON><PERSON> hình
BASE_URL = "http://localhost:8000"
API_ENDPOINT = f"{BASE_URL}/api/slides/content-slice"

def test_content_slice_api():
    """Test API cắt nội dung theo vị trí ký tự"""
    
    # Test data
    test_request = {
        "book_id": "book_123",  # Optional - có thể để None
        "lesson_id": "lesson_456",  # Thay bằng lesson_id thực tế
        "start_char": 0,
        "end_char": 100
    }
    
    print("=== Testing Content Slice API ===")
    print(f"Endpoint: {API_ENDPOINT}")
    print(f"Request data: {json.dumps(test_request, indent=2)}")
    
    try:
        # Gửi request
        response = requests.post(
            API_ENDPOINT,
            json=test_request,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"\nResponse Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API call successful!")
            print(f"Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # Hiển thị thông tin chi tiết
            if result.get("success"):
                print(f"\n📊 Content Info:")
                print(f"- Book ID: {result.get('book_id')}")
                print(f"- Lesson ID: {result.get('lesson_id')}")
                print(f"- Original length: {result.get('original_content_length')}")
                print(f"- Sliced length: {result.get('sliced_content_length')}")
                print(f"- Character range: {result.get('start_char')} - {result.get('end_char')}")
                print(f"- Collection: {result.get('collection_name')}")
                
                print(f"\n📝 Sliced Content Preview:")
                sliced_content = result.get('sliced_content', '')
                preview = sliced_content[:200] + "..." if len(sliced_content) > 200 else sliced_content
                print(f"{preview}")
            else:
                print(f"❌ API returned error: {result.get('error')}")
        else:
            print(f"❌ API call failed with status {response.status_code}")
            try:
                error_detail = response.json()
                print(f"Error detail: {json.dumps(error_detail, indent=2)}")
            except:
                print(f"Error text: {response.text}")
                
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure the server is running on localhost:8000")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_edge_cases():
    """Test các trường hợp biên"""
    
    print("\n=== Testing Edge Cases ===")
    
    # Test case 1: start_char >= end_char
    test_invalid_range = {
        "lesson_id": "lesson_456",
        "start_char": 100,
        "end_char": 50  # Invalid: end < start
    }
    
    print("\n1. Testing invalid character range (end < start):")
    try:
        response = requests.post(API_ENDPOINT, json=test_invalid_range)
        print(f"Status: {response.status_code}")
        if response.status_code != 200:
            print(f"Expected error: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test case 2: start_char vượt quá độ dài content
    test_out_of_range = {
        "lesson_id": "lesson_456",
        "start_char": 999999,  # Very large number
        "end_char": 999999 + 100
    }
    
    print("\n2. Testing out of range start_char:")
    try:
        response = requests.post(API_ENDPOINT, json=test_out_of_range)
        print(f"Status: {response.status_code}")
        if response.status_code != 200:
            print(f"Expected error: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test case 3: Missing lesson_id
    test_missing_lesson = {
        "start_char": 0,
        "end_char": 100
        # Missing lesson_id
    }
    
    print("\n3. Testing missing lesson_id:")
    try:
        response = requests.post(API_ENDPOINT, json=test_missing_lesson)
        print(f"Status: {response.status_code}")
        if response.status_code != 200:
            print(f"Expected validation error: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_content_slice_api()
    test_edge_cases()
