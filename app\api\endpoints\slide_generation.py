"""
Slide Generation API Endpoints
API xử lý JSON template từ frontend
"""

import logging
from fastapi import APIRouter, HTTPException
from typing import Dict, Any

from app.models.slide_generation_models import (
    SlideGenerationErrorCodes,
    JsonTemplateRequest,
    JsonTemplateResponse,
    SlideGenerationTaskResponse,
    ContentSliceRequest,
    ContentSliceResponse
)
from app.tasks.slide_generation_tasks import trigger_json_template_task
from app.services.json_template_service import get_json_template_service
from app.services.textbook_retrieval_service import get_textbook_retrieval_service

logger = logging.getLogger(__name__)
router = APIRouter()



@router.post("/process-json-template-async", response_model=SlideGenerationTaskResponse)
async def process_json_template_async(request: JsonTemplateRequest):
    """
    Xử lý JSON template từ frontend với nội dung bài học (Asynchronous với Celery)

    Endpoint này tạo Celery task để xử lý JSON template bất đồng bộ:
    1. Tạo task trong MongoDB với trạng thái PENDING
    2. Khởi chạy Celery task xử lý JSON template
    3. Trả về task_id để client theo dõi progress
    4. Cập nhật progress theo từng slide hoàn thành

    Args:
        request: JsonTemplateRequest với lesson_id, slides đã phân tích và config tùy chọn

    Returns:
        SlideGenerationTaskResponse: task_id và trạng thái để theo dõi
    """
    try:
        logger.info(f"=== PROCESS-JSON-TEMPLATE-ASYNC ENDPOINT CALLED ===")
        logger.info(f"Request: lesson_id={request.lesson_id}")
        logger.info(f"Slides count: {len(request.slides)}")

        # Tạo template_json từ request format mới
        template_json = {
            "slides": request.slides,
            "version": "1.0",
            "slideFormat": "16:9"
        }

        # Trigger Celery task cho JSON template processing
        task_id = await trigger_json_template_task(
            lesson_id=request.lesson_id,
            template_json=template_json,
            config_prompt=request.config_prompt,
            user_id=None,  
            book_id=None  
        )

        logger.info(f"✅ JSON template processing task created: {task_id}")

        return SlideGenerationTaskResponse(
            task_id=task_id,
            status="PENDING",
            message="Task xử lý JSON template đã được khởi tạo. Sử dụng task_id để theo dõi tiến trình."
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Unexpected error in process_json_template_async: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error_code": SlideGenerationErrorCodes.UNKNOWN_ERROR,
                "error_message": str(e),
                "success": False
            }
        )


@router.post("/content-slice", response_model=ContentSliceResponse)
async def get_content_slice(request: ContentSliceRequest):
    """
    Cắt nội dung bài học theo vị trí ký tự

    Endpoint này lấy nội dung của một bài học và cắt ra phần nội dung theo vị trí ký tự:
    1. Tìm lesson trong Qdrant collection (theo book_id nếu có)
    2. Lấy full content của lesson
    3. Cắt nội dung từ start_char đến end_char
    4. Trả về nội dung đã cắt cùng với metadata

    Args:
        request: ContentSliceRequest với book_id (optional), lesson_id, start_char, end_char

    Returns:
        ContentSliceResponse: Nội dung đã cắt và thông tin metadata
    """
    try:
        logger.info(f"=== CONTENT-SLICE ENDPOINT CALLED ===")
        logger.info(f"Request: book_id={request.book_id}, lesson_id={request.lesson_id}")
        logger.info(f"Character range: {request.start_char} - {request.end_char}")

        # Lấy textbook service
        textbook_service = get_textbook_retrieval_service()

        # Lấy nội dung lesson
        lesson_data = await textbook_service.get_lesson_content(
            lesson_id=request.lesson_id,
            book_id=request.book_id
        )

        # Lấy full content
        full_content = lesson_data.get("lesson_content", "")
        original_length = len(full_content)

        logger.info(f"Original content length: {original_length}")

        # Validate character positions
        if request.start_char >= original_length:
            raise HTTPException(
                status_code=400,
                detail={
                    "error_code": "INVALID_START_POSITION",
                    "error_message": f"start_char ({request.start_char}) vượt quá độ dài nội dung ({original_length})",
                    "success": False
                }
            )

        # Adjust end_char if it exceeds content length
        actual_end_char = min(request.end_char, original_length)

        if actual_end_char != request.end_char:
            logger.warning(f"end_char adjusted from {request.end_char} to {actual_end_char}")

        # Cắt nội dung
        sliced_content = full_content[request.start_char:actual_end_char]
        sliced_length = len(sliced_content)

        logger.info(f"✅ Content sliced successfully: {sliced_length} characters")

        return ContentSliceResponse(
            success=True,
            book_id=lesson_data.get("book_id"),
            lesson_id=request.lesson_id,
            start_char=request.start_char,
            end_char=actual_end_char,
            sliced_content=sliced_content,
            original_content_length=original_length,
            sliced_content_length=sliced_length,
            collection_name=lesson_data.get("collection_name")
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Unexpected error in get_content_slice: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error_code": SlideGenerationErrorCodes.UNKNOWN_ERROR,
                "error_message": str(e),
                "success": False
            }
        )
